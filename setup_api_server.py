"""
Setup script for IBKR API Server
Installs dependencies and tests the setup
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a Python package"""
    try:
        print(f"📦 Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def check_import(module_name, package_name=None):
    """Check if a module can be imported"""
    try:
        __import__(module_name)
        print(f"✅ {module_name} is available")
        return True
    except ImportError:
        print(f"❌ {module_name} not available")
        if package_name:
            print(f"   Install with: pip install {package_name}")
        return False

def main():
    print("🚀 IBKR API Server Setup")
    print("=" * 40)
    
    # Required packages
    packages = [
        ("flask", "Flask"),
        ("flask-cors", "Flask-CORS"), 
        ("ib-insync", "ib_insync")
    ]
    
    print("\n📦 Installing required packages...")
    all_installed = True
    
    for package, import_name in packages:
        if not install_package(package):
            all_installed = False
    
    print("\n🔍 Checking imports...")
    imports_ok = True
    
    # Check imports
    import_checks = [
        ("flask", "flask"),
        ("flask_cors", "flask-cors"),
        ("ib_insync", "ib-insync")
    ]
    
    for module, package in import_checks:
        if not check_import(module, package):
            imports_ok = False
    
    # Check our custom modules
    print("\n🔍 Checking IBKR integration modules...")
    custom_modules = [
        ("ibkr_integration", None),
        ("ibkr_trader", None)
    ]
    
    for module, _ in custom_modules:
        check_import(module)
    
    print("\n" + "=" * 40)
    
    if all_installed and imports_ok:
        print("✅ Setup completed successfully!")
        print("\n🚀 Next steps:")
        print("1. Start TWS or IB Gateway")
        print("2. Enable API access (port 7497)")
        print("3. Run: python api_server.py")
        print("4. Test: http://localhost:5000/api/ibkr?action=test")
        
        # Ask if user wants to start the server
        try:
            start_server = input("\n🤔 Start the API server now? (y/n): ").lower().strip()
            if start_server in ['y', 'yes']:
                print("\n🚀 Starting API server...")
                os.system("python api_server.py")
        except KeyboardInterrupt:
            print("\n👋 Setup complete!")
    else:
        print("❌ Setup incomplete. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
