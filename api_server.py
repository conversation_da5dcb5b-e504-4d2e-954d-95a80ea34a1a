"""
IBKR API Server - Backend for React frontend
Provides REST API endpoints for IBKR connection and trading
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import asyncio
import threading
import time
from datetime import datetime
import logging

# Import our IBKR integration
try:
    from ibkr_integration import get_ibkr_manager, IBKRManager, IBKR_AVAILABLE
    from ibkr_trader import get_ibkr_trader, IBKRTrader
except ImportError:
    IBKR_AVAILABLE = False
    print("❌ IBKR integration not available")

app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

# Global IBKR manager
ibkr_manager = None
ibkr_trader = None
connection_status = {
    'connected': False,
    'last_check': None,
    'account_id': None,
    'error': None
}

@app.route('/api/ibkr', methods=['GET'])
def ibkr_api():
    """Main IBKR API endpoint"""
    action = request.args.get('action', 'status')
    
    try:
        if action == 'connect':
            return handle_connect()
        elif action == 'disconnect':
            return handle_disconnect()
        elif action == 'status':
            return handle_status()
        elif action == 'account':
            return handle_account_info()
        elif action == 'test':
            return handle_test()
        else:
            return jsonify({
                'success': False,
                'message': f'Unknown action: {action}'
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'API error: {str(e)}'
        }), 500

def handle_connect():
    """Handle connection request"""
    global ibkr_manager, connection_status
    
    if not IBKR_AVAILABLE:
        return jsonify({
            'success': False,
            'message': 'IBKR integration not available. Install ib_insync: pip install ib_insync'
        })
    
    try:
        # Initialize manager if needed
        if not ibkr_manager:
            ibkr_manager = get_ibkr_manager()
        
        if not ibkr_manager:
            return jsonify({
                'success': False,
                'message': 'Failed to initialize IBKR manager'
            })
        
        # Attempt connection
        success = ibkr_manager.connect_sync()
        
        if success:
            connection_status.update({
                'connected': True,
                'last_check': datetime.now().isoformat(),
                'account_id': ibkr_manager.account_id,
                'error': None
            })
            
            return jsonify({
                'success': True,
                'message': 'Connected to IBKR successfully!',
                'data': {
                    'account_id': ibkr_manager.account_id,
                    'connected_at': connection_status['last_check']
                }
            })
        else:
            connection_status.update({
                'connected': False,
                'last_check': datetime.now().isoformat(),
                'error': 'Connection failed'
            })
            
            return jsonify({
                'success': False,
                'message': 'Failed to connect to IBKR. Check TWS/Gateway is running on port 7497'
            })
            
    except Exception as e:
        connection_status.update({
            'connected': False,
            'last_check': datetime.now().isoformat(),
            'error': str(e)
        })
        
        return jsonify({
            'success': False,
            'message': f'Connection error: {str(e)}'
        })

def handle_disconnect():
    """Handle disconnection request"""
    global ibkr_manager, connection_status
    
    try:
        if ibkr_manager:
            ibkr_manager.disconnect()
        
        connection_status.update({
            'connected': False,
            'last_check': datetime.now().isoformat(),
            'account_id': None,
            'error': None
        })
        
        return jsonify({
            'success': True,
            'message': 'Disconnected from IBKR'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Disconnect error: {str(e)}'
        })

def handle_status():
    """Handle status check request"""
    global ibkr_manager, connection_status
    
    # Update connection status
    if ibkr_manager:
        actual_connected = ibkr_manager.connected
        connection_status['connected'] = actual_connected
        connection_status['last_check'] = datetime.now().isoformat()
    
    return jsonify({
        'success': True,
        'data': {
            'connected': connection_status['connected'],
            'account_id': connection_status['account_id'],
            'last_check': connection_status['last_check'],
            'error': connection_status['error'],
            'ibkr_available': IBKR_AVAILABLE
        }
    })

def handle_account_info():
    """Handle account information request"""
    global ibkr_manager
    
    if not ibkr_manager or not ibkr_manager.connected:
        return jsonify({
            'success': False,
            'message': 'Not connected to IBKR'
        })
    
    try:
        account_summary = ibkr_manager.get_account_summary()
        
        # Extract key account metrics
        account_data = {}
        for key, info in account_summary.items():
            if key in ['NetLiquidation', 'AvailableFunds', 'BuyingPower', 'TotalCashValue']:
                try:
                    account_data[key.lower()] = float(info['value'])
                except (ValueError, TypeError):
                    account_data[key.lower()] = info['value']
        
        return jsonify({
            'success': True,
            'data': {
                'netLiquidation': account_data.get('netliquidation'),
                'availableFunds': account_data.get('availablefunds'),
                'buyingPower': account_data.get('buyingpower'),
                'totalCashValue': account_data.get('totalcashvalue'),
                'account_id': ibkr_manager.account_id,
                'full_summary': account_summary
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Account info error: {str(e)}'
        })

def handle_test():
    """Handle test request"""
    return jsonify({
        'success': True,
        'message': 'IBKR API server is running!',
        'data': {
            'server_time': datetime.now().isoformat(),
            'ibkr_available': IBKR_AVAILABLE,
            'endpoints': [
                '/api/ibkr?action=connect',
                '/api/ibkr?action=disconnect', 
                '/api/ibkr?action=status',
                '/api/ibkr?action=account',
                '/api/ibkr?action=test'
            ]
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'ibkr_available': IBKR_AVAILABLE
    })

@app.route('/', methods=['GET'])
def root():
    """Root endpoint with API documentation"""
    return jsonify({
        'message': 'IBKR API Server',
        'version': '1.0.0',
        'endpoints': {
            'connect': '/api/ibkr?action=connect',
            'disconnect': '/api/ibkr?action=disconnect',
            'status': '/api/ibkr?action=status',
            'account': '/api/ibkr?action=account',
            'test': '/api/ibkr?action=test',
            'health': '/api/health'
        },
        'setup_instructions': [
            '1. Install: pip install flask flask-cors ib_insync',
            '2. Start TWS/Gateway on port 7497',
            '3. Enable API in TWS settings',
            '4. Run this server: python api_server.py',
            '5. Test: http://localhost:5000/api/ibkr?action=test'
        ]
    })

if __name__ == '__main__':
    print("🚀 Starting IBKR API Server...")
    print("📡 Server will run on: http://localhost:5000")
    print("🔗 Test endpoint: http://localhost:5000/api/ibkr?action=test")
    print("📋 Make sure TWS/Gateway is running on port 7497")
    print()
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Start the Flask server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
