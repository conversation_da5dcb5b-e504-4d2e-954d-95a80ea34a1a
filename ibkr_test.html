<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏦 IBKR Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 16px;
        }
        .btn:hover { background: #0056b3; }
        .btn:disabled { background: #6c757d; cursor: not-allowed; }
        .status { font-size: 18px; font-weight: bold; margin: 10px 0; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .card { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .loading { color: #6c757d; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🏦 IBKR Connection Test</h1>

    <div class="container">
        <div class="alert alert-error">
            <h3>🚨 IMPORTANT: NO API KEY NEEDED!</h3>
            <p>IBKR doesn't use API keys! You just need: ✅ Your login ✅ IB Gateway ✅ Account number</p>
        </div>
    </div>

    <div class="container">
        <h2>📋 SUPER SIMPLE 3-STEP SETUP</h2>
        <div class="grid">
            <div class="card">
                <h3>1️⃣ Enable API</h3>
                <p>In Client Portal:</p>
                <ul>
                    <li>Settings → API</li>
                    <li>Check "Enable API"</li>
                    <li>Port: 7497</li>
                </ul>
            </div>
            <div class="card">
                <h3>2️⃣ Download Gateway</h3>
                <p>Get IB Gateway:</p>
                <ul>
                    <li>Download from IBKR</li>
                    <li>Install & run</li>
                    <li>Login normally</li>
                </ul>
            </div>
            <div class="card">
                <h3>3️⃣ Get Account #</h3>
                <p>Find your number:</p>
                <ul>
                    <li>DU123456 (paper)</li>
                    <li>U123456 (live)</li>
                    <li>Top of Client Portal</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🔍 Connection Test</h3>
        <div class="status" id="connectionStatus">Not Connected</div>
        <div id="errorMessage"></div>
        
        <button class="btn" onclick="testConnection()" id="connectBtn">🔌 Connect to IBKR</button>
        <button class="btn" onclick="checkStatus()">📊 Check Status</button>
        <button class="btn" onclick="testServer()">🧪 Test Server</button>
        
        <div id="accountInfo" style="margin-top: 20px;"></div>
    </div>

    <div class="container">
        <h3>🔧 Server Status</h3>
        <div id="serverStatus">Checking...</div>
        <pre id="serverResponse"></pre>
    </div>

    <div class="container">
        <h3>🚀 Quick Links</h3>
        <div class="grid">
            <div class="card">
                <strong>📱 Client Portal</strong><br>
                <a href="https://www.interactivebrokers.com/sso/Login" target="_blank">Enable API Access</a>
            </div>
            <div class="card">
                <strong>💻 Download IB Gateway</strong><br>
                <a href="https://www.interactivebrokers.com/en/trading/ib-api.php" target="_blank">Get IB Gateway</a>
            </div>
            <div class="card">
                <strong>📚 Paper Trading</strong><br>
                <span>Enable in Client Portal Settings</span>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        async function apiCall(action) {
            try {
                const response = await fetch(`${API_BASE}/api/ibkr?action=${action}`);
                const result = await response.json();
                return result;
            } catch (error) {
                return {
                    success: false,
                    message: `Network error: ${error.message}`
                };
            }
        }
        
        async function testConnection() {
            const btn = document.getElementById('connectBtn');
            const status = document.getElementById('connectionStatus');
            const errorDiv = document.getElementById('errorMessage');
            
            btn.disabled = true;
            btn.textContent = '🔄 Connecting...';
            status.textContent = 'Connecting...';
            status.className = 'status loading';
            errorDiv.innerHTML = '';
            
            try {
                console.log('🔌 Testing IBKR connection...');
                const result = await apiCall('connect');
                
                if (result.success) {
                    status.textContent = '✅ Connected to IBKR!';
                    status.className = 'status';
                    status.style.color = '#28a745';
                    console.log('✅ IBKR Connected successfully!');
                    
                    // Get account info after connection
                    setTimeout(getAccountInfo, 2000);
                } else {
                    status.textContent = '❌ Connection Failed';
                    status.style.color = '#dc3545';
                    errorDiv.innerHTML = `<div class="alert alert-error"><strong>Error:</strong> ${result.message}</div>`;
                    console.error('❌ IBKR Connection failed:', result.message);
                }
            } catch (error) {
                status.textContent = '❌ Connection Error';
                status.style.color = '#dc3545';
                errorDiv.innerHTML = `<div class="alert alert-error"><strong>Error:</strong> ${error.message}</div>`;
                console.error('❌ Connection error:', error);
            } finally {
                btn.disabled = false;
                btn.textContent = '🔌 Connect to IBKR';
            }
        }
        
        async function checkStatus() {
            try {
                const result = await apiCall('status');
                const status = document.getElementById('connectionStatus');
                
                if (result.success) {
                    const connected = result.data.connected;
                    status.textContent = connected ? '✅ Connected' : '❌ Disconnected';
                    status.style.color = connected ? '#28a745' : '#dc3545';
                    
                    if (result.data.account_id) {
                        status.textContent += ` (${result.data.account_id})`;
                    }
                } else {
                    status.textContent = '❌ Status Check Failed';
                    status.style.color = '#dc3545';
                }
            } catch (error) {
                console.error('Status check error:', error);
            }
        }
        
        async function getAccountInfo() {
            try {
                const result = await apiCall('account');
                const accountDiv = document.getElementById('accountInfo');
                
                if (result.success && result.data) {
                    const data = result.data;
                    accountDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>💰 Account Information</h4>
                            <div class="grid">
                                <div><strong>Net Liquidation:</strong><br>$${(data.netLiquidation || 0).toLocaleString()}</div>
                                <div><strong>Available Funds:</strong><br>$${(data.availableFunds || 0).toLocaleString()}</div>
                                <div><strong>Buying Power:</strong><br>$${(data.buyingPower || 0).toLocaleString()}</div>
                                <div><strong>Total Cash:</strong><br>$${(data.totalCashValue || 0).toLocaleString()}</div>
                            </div>
                        </div>
                    `;
                } else {
                    accountDiv.innerHTML = `<div class="alert alert-warning">Account info not available yet</div>`;
                }
            } catch (error) {
                console.error('Account info error:', error);
            }
        }
        
        async function testServer() {
            const statusDiv = document.getElementById('serverStatus');
            const responseDiv = document.getElementById('serverResponse');
            
            statusDiv.textContent = 'Testing server...';
            
            try {
                const result = await apiCall('test');
                
                if (result.success) {
                    statusDiv.innerHTML = '<span style="color: #28a745;">✅ Server is running!</span>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                } else {
                    statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Server test failed</span>';
                    responseDiv.textContent = JSON.stringify(result, null, 2);
                }
            } catch (error) {
                statusDiv.innerHTML = '<span style="color: #dc3545;">❌ Server not responding</span>';
                responseDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        // Test server on page load
        window.onload = function() {
            testServer();
            checkStatus();
        };
    </script>
</body>
</html>
