"""
Interactive Brokers TWS API Integration
Complete implementation for real trading through IBKR
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import logging

try:
    from ib_insync import IB, Stock, Option, Future, Forex, Index, CFD
    from ib_insync import MarketOrder, LimitOrder, StopOrder, Order
    from ib_insync import Contract, Trade, Position, AccountValue
    from ib_insync import util
    IBKR_AVAILABLE = True
except ImportError:
    IBKR_AVAILABLE = False
    print("[IBKR] ib_insync not available. Install with: pip install ib_insync")

class IBKRManager:
    """Interactive Brokers connection and trading manager"""
    
    def __init__(self, host='127.0.0.1', port=7497, client_id=1):
        """
        Initialize IBKR manager
        
        Args:
            host: TWS/Gateway host (default: localhost)
            port: TWS port (7497 for paper, 7496 for live)
            client_id: Unique client identifier
        """
        if not IBKR_AVAILABLE:
            raise ImportError("ib_insync library not available")
            
        self.ib = IB()
        self.host = host
        self.port = port
        self.client_id = client_id
        self.connected = False
        self.account_id = None
        self.positions = {}
        self.orders = {}
        self.market_data_subscriptions = {}
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Connection monitoring
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds
        
    async def connect(self) -> bool:
        """Connect to IBKR TWS/Gateway"""
        try:
            if self.connected:
                return True
                
            self.logger.info(f"[IBKR] Connecting to {self.host}:{self.port} (Client ID: {self.client_id})")
            
            # Connect with timeout
            await asyncio.wait_for(
                self.ib.connectAsync(self.host, self.port, self.client_id),
                timeout=10.0
            )
            
            self.connected = True
            self.reconnect_attempts = 0
            
            # Get account information
            accounts = self.ib.managedAccounts()
            if accounts:
                self.account_id = accounts[0]
                self.logger.info(f"[IBKR] ✅ Connected! Account: {self.account_id}")
            else:
                self.logger.warning("[IBKR] ⚠️ Connected but no accounts found")
            
            # Setup event handlers
            self.ib.errorEvent += self._on_error
            self.ib.disconnectedEvent += self._on_disconnect
            
            return True
            
        except asyncio.TimeoutError:
            self.logger.error("[IBKR] ❌ Connection timeout")
            return False
        except Exception as e:
            self.logger.error(f"[IBKR] ❌ Connection failed: {e}")
            return False
    
    def connect_sync(self) -> bool:
        """Synchronous connection wrapper"""
        return asyncio.run(self.connect())
    
    def disconnect(self):
        """Disconnect from IBKR"""
        try:
            if self.connected:
                self.ib.disconnect()
                self.connected = False
                self.logger.info("[IBKR] Disconnected")
        except Exception as e:
            self.logger.error(f"[IBKR] Disconnect error: {e}")
    
    def _on_error(self, reqId, errorCode, errorString, contract):
        """Handle IBKR errors"""
        self.logger.error(f"[IBKR] Error {errorCode}: {errorString}")
        
        # Handle specific error codes
        if errorCode in [1100, 1101, 1102]:  # Connection lost
            self.connected = False
            self._attempt_reconnect()
    
    def _on_disconnect(self):
        """Handle disconnection"""
        self.logger.warning("[IBKR] Disconnected from TWS/Gateway")
        self.connected = False
        self._attempt_reconnect()
    
    def _attempt_reconnect(self):
        """Attempt to reconnect to IBKR"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error("[IBKR] Max reconnection attempts reached")
            return
            
        self.reconnect_attempts += 1
        self.logger.info(f"[IBKR] Reconnection attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}")
        
        time.sleep(self.reconnect_delay)
        asyncio.create_task(self.connect())
    
    def get_account_summary(self) -> Dict[str, Any]:
        """Get account summary information"""
        if not self.connected:
            return {}
            
        try:
            summary = self.ib.accountSummary()
            account_info = {}
            
            for item in summary:
                account_info[item.tag] = {
                    'value': item.value,
                    'currency': item.currency
                }
            
            return account_info
            
        except Exception as e:
            self.logger.error(f"[IBKR] Account summary error: {e}")
            return {}
    
    def get_positions(self) -> List[Position]:
        """Get current positions"""
        if not self.connected:
            return []
            
        try:
            positions = self.ib.positions()
            self.positions = {pos.contract.symbol: pos for pos in positions}
            return positions
            
        except Exception as e:
            self.logger.error(f"[IBKR] Positions error: {e}")
            return []
    
    def create_stock_contract(self, symbol: str, exchange: str = 'SMART', currency: str = 'USD') -> Stock:
        """Create a stock contract"""
        return Stock(symbol, exchange, currency)
    
    def create_option_contract(self, symbol: str, expiry: str, strike: float, 
                             right: str, exchange: str = 'SMART', currency: str = 'USD') -> Option:
        """Create an option contract"""
        return Option(symbol, expiry, strike, right, exchange, currency)
    
    def create_forex_contract(self, pair: str) -> Forex:
        """Create a forex contract (e.g., 'EURUSD')"""
        return Forex(pair)
    
    def get_market_data(self, contract: Contract, snapshot: bool = False) -> Optional[Any]:
        """Get market data for a contract"""
        if not self.connected:
            return None
            
        try:
            if snapshot:
                # Get snapshot data
                ticker = self.ib.reqMktData(contract, snapshot=True)
                self.ib.sleep(2)  # Wait for data
                return ticker
            else:
                # Subscribe to real-time data
                ticker = self.ib.reqMktData(contract)
                self.market_data_subscriptions[contract.symbol] = ticker
                return ticker
                
        except Exception as e:
            self.logger.error(f"[IBKR] Market data error for {contract.symbol}: {e}")
            return None
    
    def place_market_order(self, contract: Contract, quantity: int, action: str = 'BUY') -> Optional[Trade]:
        """Place a market order"""
        if not self.connected:
            self.logger.error("[IBKR] Not connected - cannot place order")
            return None
            
        try:
            order = MarketOrder(action, abs(quantity))
            trade = self.ib.placeOrder(contract, order)
            
            self.logger.info(f"[IBKR] Market order placed: {action} {quantity} {contract.symbol}")
            self.orders[trade.order.orderId] = trade
            
            return trade
            
        except Exception as e:
            self.logger.error(f"[IBKR] Market order error: {e}")
            return None
    
    def place_limit_order(self, contract: Contract, quantity: int, price: float, 
                         action: str = 'BUY') -> Optional[Trade]:
        """Place a limit order"""
        if not self.connected:
            self.logger.error("[IBKR] Not connected - cannot place order")
            return None
            
        try:
            order = LimitOrder(action, abs(quantity), price)
            trade = self.ib.placeOrder(contract, order)
            
            self.logger.info(f"[IBKR] Limit order placed: {action} {quantity} {contract.symbol} @ ${price}")
            self.orders[trade.order.orderId] = trade
            
            return trade
            
        except Exception as e:
            self.logger.error(f"[IBKR] Limit order error: {e}")
            return None
    
    def cancel_order(self, order_id: int) -> bool:
        """Cancel an order"""
        if not self.connected:
            return False
            
        try:
            if order_id in self.orders:
                trade = self.orders[order_id]
                self.ib.cancelOrder(trade.order)
                self.logger.info(f"[IBKR] Order {order_id} cancelled")
                return True
            else:
                self.logger.warning(f"[IBKR] Order {order_id} not found")
                return False
                
        except Exception as e:
            self.logger.error(f"[IBKR] Cancel order error: {e}")
            return False
    
    def get_historical_data(self, contract: Contract, duration: str = '1 D', 
                           bar_size: str = '1 min', what_to_show: str = 'TRADES') -> Optional[Any]:
        """Get historical data"""
        if not self.connected:
            return None
            
        try:
            bars = self.ib.reqHistoricalData(
                contract, 
                endDateTime='', 
                durationStr=duration,
                barSizeSetting=bar_size,
                whatToShow=what_to_show,
                useRTH=True
            )
            
            return bars
            
        except Exception as e:
            self.logger.error(f"[IBKR] Historical data error: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """Check if market is open"""
        # Simple market hours check (US Eastern Time)
        now = datetime.now()
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        
        # Weekend check
        if weekday >= 5:  # Saturday or Sunday
            return False
            
        # Market hours: 9:30 AM - 4:00 PM ET (approximate)
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return market_open <= now <= market_close
    
    def __enter__(self):
        """Context manager entry"""
        self.connect_sync()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()

# Global IBKR manager instance
ibkr_manager = None

def get_ibkr_manager() -> Optional[IBKRManager]:
    """Get global IBKR manager instance"""
    global ibkr_manager
    if ibkr_manager is None and IBKR_AVAILABLE:
        ibkr_manager = IBKRManager()
    return ibkr_manager

def test_ibkr_connection() -> bool:
    """Test IBKR connection"""
    try:
        manager = get_ibkr_manager()
        if not manager:
            print("[IBKR] ❌ IBKR manager not available")
            return False
            
        if manager.connect_sync():
            print("[IBKR] ✅ Connection successful!")
            
            # Test account access
            account_info = manager.get_account_summary()
            if account_info:
                print(f"[IBKR] ✅ Account access confirmed")
                print(f"[IBKR] Account ID: {manager.account_id}")
                
                # Show key account metrics
                if 'NetLiquidation' in account_info:
                    net_liq = account_info['NetLiquidation']
                    print(f"[IBKR] Net Liquidation: {net_liq['value']} {net_liq['currency']}")
                
                if 'AvailableFunds' in account_info:
                    avail_funds = account_info['AvailableFunds']
                    print(f"[IBKR] Available Funds: {avail_funds['value']} {avail_funds['currency']}")
            
            manager.disconnect()
            return True
        else:
            print("[IBKR] ❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"[IBKR] ❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the connection
    test_ibkr_connection()
