"""
IBKR Trading Executor - Integrates with existing trading system
Handles real trading execution through Interactive Brokers
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any, Tuple
import asyncio

from ibkr_integration import get_ibkr_manager, IBKRManager, IBKR_AVAILABLE

class IBKRTrader:
    """IBKR trading execution system"""
    
    def __init__(self):
        self.manager = get_ibkr_manager() if IBKR_AVAILABLE else None
        self.active_trades = {}
        self.position_monitor_active = False
        self.risk_limits = {
            'max_position_value': 10000,  # Max $10k per position
            'max_daily_loss': 1000,       # Max $1k daily loss
            'max_positions': 10           # Max 10 open positions
        }
        self.daily_pnl = 0.0
        self.trade_count = 0
        
    def connect(self) -> bool:
        """Connect to IBKR"""
        if not self.manager:
            print("[IBKR-TRADER] ❌ IBKR not available")
            return False
            
        success = self.manager.connect_sync()
        if success:
            print("[IBKR-TRADER] ✅ Connected to IBKR")
            self.start_position_monitor()
        return success
    
    def disconnect(self):
        """Disconnect from IBKR"""
        if self.manager:
            self.manager.disconnect()
            print("[IBKR-TRADER] Disconnected from IBKR")
    
    def start_position_monitor(self):
        """Start position monitoring thread"""
        if not self.position_monitor_active:
            self.position_monitor_active = True
            monitor_thread = threading.Thread(target=self._position_monitor_loop, daemon=True)
            monitor_thread.start()
            print("[IBKR-TRADER] Position monitor started")
    
    def _position_monitor_loop(self):
        """Monitor positions and manage risk"""
        while self.position_monitor_active:
            try:
                if self.manager and self.manager.connected:
                    self._update_positions()
                    self._check_risk_limits()
                    self._manage_stop_losses()
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                print(f"[IBKR-TRADER] Position monitor error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _update_positions(self):
        """Update current positions"""
        try:
            positions = self.manager.get_positions()
            for position in positions:
                symbol = position.contract.symbol
                self.active_trades[symbol] = {
                    'position': position,
                    'last_update': datetime.now()
                }
        except Exception as e:
            print(f"[IBKR-TRADER] Position update error: {e}")
    
    def _check_risk_limits(self):
        """Check and enforce risk limits"""
        try:
            # Check daily loss limit
            if abs(self.daily_pnl) > self.risk_limits['max_daily_loss']:
                print(f"[IBKR-TRADER] ⚠️ Daily loss limit reached: ${self.daily_pnl:.2f}")
                self._close_all_positions("Daily loss limit")
                return
            
            # Check position count
            if len(self.active_trades) > self.risk_limits['max_positions']:
                print(f"[IBKR-TRADER] ⚠️ Too many positions: {len(self.active_trades)}")
                # Close oldest positions
                self._close_excess_positions()
                
        except Exception as e:
            print(f"[IBKR-TRADER] Risk check error: {e}")
    
    def _manage_stop_losses(self):
        """Manage stop losses for active positions"""
        # Implementation for stop loss management
        pass
    
    def execute_stock_trade(self, symbol: str, action: str, quantity: int, 
                           order_type: str = 'MARKET', price: float = None) -> Optional[Any]:
        """Execute a stock trade"""
        if not self.manager or not self.manager.connected:
            print("[IBKR-TRADER] ❌ Not connected to IBKR")
            return None
        
        try:
            # Create stock contract
            contract = self.manager.create_stock_contract(symbol)
            
            # Validate contract
            contract_details = self.manager.ib.reqContractDetails(contract)
            if not contract_details:
                print(f"[IBKR-TRADER] ❌ Invalid contract: {symbol}")
                return None
            
            # Check risk limits
            if not self._check_trade_risk(symbol, quantity, price or 0):
                return None
            
            # Place order
            if order_type.upper() == 'MARKET':
                trade = self.manager.place_market_order(contract, quantity, action.upper())
            elif order_type.upper() == 'LIMIT' and price:
                trade = self.manager.place_limit_order(contract, quantity, price, action.upper())
            else:
                print(f"[IBKR-TRADER] ❌ Invalid order type: {order_type}")
                return None
            
            if trade:
                print(f"[IBKR-TRADER] ✅ Order placed: {action} {quantity} {symbol}")
                self.trade_count += 1
                return trade
            else:
                print(f"[IBKR-TRADER] ❌ Order failed: {symbol}")
                return None
                
        except Exception as e:
            print(f"[IBKR-TRADER] Trade execution error: {e}")
            return None
    
    def execute_arbitrage_trade(self, opportunity: Dict[str, Any]) -> bool:
        """Execute arbitrage trade opportunity"""
        try:
            symbol = opportunity.get('symbol', 'BTC')
            buy_exchange = opportunity.get('buy_exchange', '')
            sell_exchange = opportunity.get('sell_exchange', '')
            quantity = opportunity.get('recommended_size', 0.001)
            
            print(f"[IBKR-TRADER] 🔄 Executing arbitrage: {symbol}")
            print(f"[IBKR-TRADER] Buy: {buy_exchange}, Sell: {sell_exchange}")
            
            # For crypto arbitrage, we need to handle differently
            # IBKR doesn't directly support crypto arbitrage between external exchanges
            # This would require integration with crypto exchange APIs
            
            # For now, simulate with equivalent stock trades
            if symbol.startswith('BTC'):
                # Use Bitcoin-related stocks as proxy (MSTR, COIN, etc.)
                proxy_symbols = ['MSTR', 'COIN', 'RIOT']
                for proxy in proxy_symbols:
                    # Small position in Bitcoin-related stock
                    trade = self.execute_stock_trade(proxy, 'BUY', 10, 'MARKET')
                    if trade:
                        print(f"[IBKR-TRADER] ✅ Proxy arbitrage executed: {proxy}")
                        return True
            
            return False
            
        except Exception as e:
            print(f"[IBKR-TRADER] Arbitrage execution error: {e}")
            return False
    
    def execute_options_strategy(self, strategy: str, symbol: str, **kwargs) -> bool:
        """Execute options trading strategy"""
        if not self.manager or not self.manager.connected:
            return False
        
        try:
            print(f"[IBKR-TRADER] 📊 Executing {strategy} on {symbol}")
            
            # Get current stock price for strike selection
            stock_contract = self.manager.create_stock_contract(symbol)
            ticker = self.manager.get_market_data(stock_contract, snapshot=True)
            
            if not ticker or not ticker.last:
                print(f"[IBKR-TRADER] ❌ Cannot get price for {symbol}")
                return False
            
            current_price = ticker.last
            
            if strategy.lower() == 'straddle':
                return self._execute_straddle(symbol, current_price, **kwargs)
            elif strategy.lower() == 'strangle':
                return self._execute_strangle(symbol, current_price, **kwargs)
            elif strategy.lower() == 'iron_condor':
                return self._execute_iron_condor(symbol, current_price, **kwargs)
            else:
                print(f"[IBKR-TRADER] ❌ Unknown strategy: {strategy}")
                return False
                
        except Exception as e:
            print(f"[IBKR-TRADER] Options strategy error: {e}")
            return False
    
    def _execute_straddle(self, symbol: str, current_price: float, **kwargs) -> bool:
        """Execute straddle strategy"""
        try:
            # Calculate expiration (next Friday)
            expiry = self._get_next_friday()
            strike = round(current_price)  # At-the-money
            
            # Create call and put contracts
            call_contract = self.manager.create_option_contract(symbol, expiry, strike, 'C')
            put_contract = self.manager.create_option_contract(symbol, expiry, strike, 'P')
            
            # Buy both call and put
            call_trade = self.manager.place_market_order(call_contract, 1, 'BUY')
            put_trade = self.manager.place_market_order(put_contract, 1, 'BUY')
            
            if call_trade and put_trade:
                print(f"[IBKR-TRADER] ✅ Straddle executed: {symbol} {strike} {expiry}")
                return True
            else:
                print(f"[IBKR-TRADER] ❌ Straddle failed: {symbol}")
                return False
                
        except Exception as e:
            print(f"[IBKR-TRADER] Straddle error: {e}")
            return False
    
    def _execute_strangle(self, symbol: str, current_price: float, **kwargs) -> bool:
        """Execute strangle strategy"""
        try:
            expiry = self._get_next_friday()
            call_strike = round(current_price * 1.05)  # 5% OTM call
            put_strike = round(current_price * 0.95)   # 5% OTM put
            
            call_contract = self.manager.create_option_contract(symbol, expiry, call_strike, 'C')
            put_contract = self.manager.create_option_contract(symbol, expiry, put_strike, 'P')
            
            call_trade = self.manager.place_market_order(call_contract, 1, 'BUY')
            put_trade = self.manager.place_market_order(put_contract, 1, 'BUY')
            
            if call_trade and put_trade:
                print(f"[IBKR-TRADER] ✅ Strangle executed: {symbol}")
                return True
            else:
                return False
                
        except Exception as e:
            print(f"[IBKR-TRADER] Strangle error: {e}")
            return False
    
    def _execute_iron_condor(self, symbol: str, current_price: float, **kwargs) -> bool:
        """Execute iron condor strategy"""
        # Implementation for iron condor
        print(f"[IBKR-TRADER] Iron condor not yet implemented for {symbol}")
        return False
    
    def _get_next_friday(self) -> str:
        """Get next Friday's date in YYYYMMDD format"""
        today = datetime.now()
        days_ahead = 4 - today.weekday()  # Friday is 4
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7
        next_friday = today + timedelta(days=days_ahead)
        return next_friday.strftime('%Y%m%d')
    
    def _check_trade_risk(self, symbol: str, quantity: int, price: float) -> bool:
        """Check if trade meets risk requirements"""
        try:
            position_value = abs(quantity * price) if price > 0 else abs(quantity * 100)  # Estimate
            
            if position_value > self.risk_limits['max_position_value']:
                print(f"[IBKR-TRADER] ❌ Position too large: ${position_value:.2f}")
                return False
            
            if len(self.active_trades) >= self.risk_limits['max_positions']:
                print(f"[IBKR-TRADER] ❌ Too many positions: {len(self.active_trades)}")
                return False
            
            return True
            
        except Exception as e:
            print(f"[IBKR-TRADER] Risk check error: {e}")
            return False
    
    def _close_all_positions(self, reason: str):
        """Close all open positions"""
        print(f"[IBKR-TRADER] 🚨 Closing all positions: {reason}")
        # Implementation for closing all positions
        pass
    
    def _close_excess_positions(self):
        """Close excess positions to stay within limits"""
        # Implementation for closing excess positions
        pass
    
    def get_account_status(self) -> Dict[str, Any]:
        """Get current account status"""
        if not self.manager or not self.manager.connected:
            return {}
        
        try:
            account_info = self.manager.get_account_summary()
            positions = self.manager.get_positions()
            
            return {
                'connected': True,
                'account_id': self.manager.account_id,
                'account_info': account_info,
                'positions_count': len(positions),
                'active_trades': len(self.active_trades),
                'daily_pnl': self.daily_pnl,
                'trade_count': self.trade_count
            }
            
        except Exception as e:
            print(f"[IBKR-TRADER] Account status error: {e}")
            return {'connected': False, 'error': str(e)}

# Global IBKR trader instance
ibkr_trader = None

def get_ibkr_trader() -> Optional[IBKRTrader]:
    """Get global IBKR trader instance"""
    global ibkr_trader
    if ibkr_trader is None and IBKR_AVAILABLE:
        ibkr_trader = IBKRTrader()
    return ibkr_trader

def test_ibkr_trading() -> bool:
    """Test IBKR trading functionality"""
    try:
        trader = get_ibkr_trader()
        if not trader:
            print("[IBKR-TRADER] ❌ IBKR trader not available")
            return False
        
        if trader.connect():
            print("[IBKR-TRADER] ✅ Trading system connected")
            
            # Test account status
            status = trader.get_account_status()
            if status.get('connected'):
                print(f"[IBKR-TRADER] ✅ Account: {status.get('account_id')}")
                print(f"[IBKR-TRADER] Positions: {status.get('positions_count', 0)}")
            
            trader.disconnect()
            return True
        else:
            print("[IBKR-TRADER] ❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"[IBKR-TRADER] ❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    # Test the trading system
    test_ibkr_trading()
