"""
Test IBKR Integration
Simple test script to verify IBKR connection and functionality
"""

import sys
import time
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("=" * 50)
    print("TESTING IBKR INTEGRATION")
    print("=" * 50)
    
    try:
        print("[1/4] Testing ib_insync import...")
        from ib_insync import IB, Stock, MarketOrder
        print("✅ ib_insync imported successfully")
    except ImportError as e:
        print(f"❌ ib_insync import failed: {e}")
        print("💡 Install with: pip install ib_insync")
        return False
    
    try:
        print("[2/4] Testing ibkr_integration import...")
        from ibkr_integration import IBKRManager, test_ibkr_connection
        print("✅ ibkr_integration imported successfully")
    except ImportError as e:
        print(f"❌ ibkr_integration import failed: {e}")
        return False
    
    try:
        print("[3/4] Testing ibkr_trader import...")
        from ibkr_trader import IBKRTrader, test_ibkr_trading
        print("✅ ibkr_trader imported successfully")
    except ImportError as e:
        print(f"❌ ibkr_trader import failed: {e}")
        return False
    
    print("[4/4] All imports successful! ✅")
    return True

def test_connection():
    """Test IBKR connection"""
    print("\n" + "=" * 50)
    print("TESTING IBKR CONNECTION")
    print("=" * 50)
    
    try:
        from ibkr_integration import test_ibkr_connection
        print("Testing connection to IBKR TWS/Gateway...")
        print("(Make sure TWS or Gateway is running on port 7497)")
        
        success = test_ibkr_connection()
        if success:
            print("✅ IBKR connection test PASSED")
            return True
        else:
            print("❌ IBKR connection test FAILED")
            print("\n📋 Troubleshooting checklist:")
            print("1. Is TWS or IB Gateway running?")
            print("2. Is API access enabled in TWS settings?")
            print("3. Is socket port set to 7497 (paper trading)?")
            print("4. Is 127.0.0.1 in trusted IPs?")
            print("5. Is paper trading account active?")
            return False
            
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        return False

def test_trading_system():
    """Test IBKR trading system"""
    print("\n" + "=" * 50)
    print("TESTING IBKR TRADING SYSTEM")
    print("=" * 50)
    
    try:
        from ibkr_trader import test_ibkr_trading
        print("Testing IBKR trading functionality...")
        
        success = test_ibkr_trading()
        if success:
            print("✅ IBKR trading system test PASSED")
            return True
        else:
            print("❌ IBKR trading system test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Trading system test error: {e}")
        return False

def test_integration_with_main():
    """Test integration with main trading system"""
    print("\n" + "=" * 50)
    print("TESTING MAIN SYSTEM INTEGRATION")
    print("=" * 50)
    
    try:
        print("Testing integration with high_freq_leveraged_trader.py...")
        
        # Test if main system can import IBKR modules
        import high_freq_leveraged_trader as main_trader
        
        # Check if IBKR_AVAILABLE is set correctly
        if hasattr(main_trader, 'IBKR_AVAILABLE'):
            if main_trader.IBKR_AVAILABLE:
                print("✅ IBKR integration detected in main system")
            else:
                print("❌ IBKR integration not available in main system")
                return False
        else:
            print("❌ IBKR_AVAILABLE flag not found in main system")
            return False
        
        print("✅ Main system integration test PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Main system integration test error: {e}")
        return False

def run_all_tests():
    """Run all IBKR tests"""
    print(f"IBKR Integration Test Suite")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Import Tests", test_imports),
        ("Connection Test", test_connection),
        ("Trading System Test", test_trading_system),
        ("Main Integration Test", test_integration_with_main)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! IBKR integration is ready!")
        print("\n📋 Next steps:")
        print("1. Start TWS or IB Gateway")
        print("2. Enable API access in TWS settings")
        print("3. Run the main trading system")
        print("4. Change execution mode to 'IBKR'")
        print("5. Test with small positions first")
    else:
        print("⚠️ Some tests failed. Please fix issues before using IBKR.")
        print("\n💡 Common solutions:")
        print("- Install missing dependencies: pip install ib_insync")
        print("- Start TWS/Gateway software")
        print("- Enable API access in TWS settings")
        print("- Check firewall settings")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test suite crashed: {e}")
        sys.exit(1)
